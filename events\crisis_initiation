namespace = crisis_initiator

# Main crisis initiation event
country_event = {
    id = crisis_initiator.1
    title = "DIPLOMATIC_CRISIS_INITIATE_TITLE"
    desc = "DIPLOMATIC_CRISIS_INITIATE_DESC"
    picture = GFX_event_tension_rising
    
    is_triggered_only = yes
    
    trigger = {
        NOT = { has_global_variable = { name = crisis_active value = 1 } }
        is_great_power = yes
        global_tension < 0.1
        NOT = { is_at_war = yes }
        has_political_power > 100
    }
    
    option = {
        name = "INITIATE_CRISIS"
        # Check if player can initiate crisis
        trigger = {
            ROOT = {
                is_great_power = yes
                global_tension < 0.1
                NOT = { is_at_war = yes }
            }
        }
        
        # Start crisis initialization
        scripted_effect = initialize_crisis
        
        # Open crisis interface
        open_crisis_window = yes
        
        # Notify all great powers
        every_country = {
            limit = { is_great_power = yes }
            country_event = { id = crisis_initiator.2 days = 1 }
        }
    }
    
    option = {
        name = "NOT_NOW"
        # AI or player chooses not to initiate
    }
}

# Notification for great powers
country_event = {
    id = crisis_initiator.2
    title = "CRISIS_NOTIFICATION_TITLE"
    desc = "CRISIS_NOTIFICATION_DESC"
    
    is_triggered_only = yes
    
    trigger = {
        is_great_power = yes
        has_global_variable = { name = crisis_active value = 1 }
        NOT = { ROOT = { has_country_flag = crisis_participant } }
    }
    
    option = {
        name = "CONSIDER_PARTICIPATION"
        # Add decision to consider participation
        add_country_flag = crisis_aware
    }
}