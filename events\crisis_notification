namespace = crisis_notification

country_event = {
    id = crisis_notification.1
    title = "CRISIS_BEGAN_TITLE"
    desc = "CRISIS_BEGAN_DESC"
    picture = GFX_event_tension_rising
    
    is_triggered_only = yes
    
    trigger = {
        is_great_power = yes
        has_country_flag = crisis_aware
        NOT = { ROOT = { has_country_flag = crisis_participant } }
    }
    
    option = {
        name = "OBSERVE_CRISIS"
        # Just observe, don't participate
        add_country_flag = crisis_observer
    }
    
    option = {
        name = "CONSIDER_PARTICIPATION"
        trigger = {
            can_participate_in_crisis = yes
        }
        
        # Add participation decision
        add_country_flag = crisis_can_participate
    }
}