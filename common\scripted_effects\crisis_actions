# Demonstration of strength action
demonstration_strength = {
    # Check if country has enough influence
    if = {
        limit = {
            has_influence > 25
        }
        
        # Cost influence
        add_influence = -25
        
        # Increase side strength
        if = {
            limit = { has_country_flag = crisis_attacker }
            change_global_variable = {
                name = crisis_attacker_side
                value = 10
            }
        }
        else = {
            change_global_variable = {
                name = crisis_defender_side
                value = 10
            }
        }
        
        # Increase intensity
        change_global_variable = {
            name = crisis_intensity
            value = 5
        }
        
        # Add notification
        create_country_event = {
            id = crisis_actions.1
            days = 1
        }
    }
}

# Discharge tension action
discharge_tension = {
    if = {
        limit = {
            has_influence > 20
        }
        
        add_influence = -20
        
        change_global_variable = {
            name = crisis_intensity
            value = -5
        }
        
        create_country_event = {
            id = crisis_actions.2
            days = 1
        }
    }
}

# Chauvinist lunge action
chauvinist_lunge = {
    if = {
        limit = {
            has_influence > 15
        }
        
        add_influence = -15
        
        change_global_variable = {
            name = crisis_intensity
            value = 5
        }
        
        add_global_tension = 1
        add_war_support = 0.05
        add_influence = 5
        
        create_country_event = {
            id = crisis_actions.3
            days = 1
        }
    }
}