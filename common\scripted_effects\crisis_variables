# Initialize crisis system
initialize_crisis = {
    set_global_variable = { name = crisis_active value = 1 }
    set_global_variable = { name = crisis_intensity value = 0 }
    set_global_variable = { name = crisis_attacker_side value = 0 }
    set_global_variable = { name = crisis_defender_side value = 0 }
    set_global_variable = { name = crisis_disputed_states value = 0 }
    set_global_variable = { name = crisis_initiator value = ROOT }
    set_global_variable = { name = crisis_victim value = FROM }
    
    # Set up crisis flags for all countries
    every_country = {
        set_country_flag = crisis_not_participating
    }
}

# Updated intensity mechanics
increase_crisis_intensity = {
    change_global_variable = {
        name = crisis_intensity
        value = 1
    }
    
    # Check if crisis should resolve
    if = {
        limit = {
            check_global_variable = { name = crisis_intensity value >= 100 }
        }
        country_event = { id = crisis_resolution.1 days = 1 }
    }
}

# New effect for 1.16 compatibility
calculate_crisis_power = {
    # Economic power (updated for 1.16)
    var:economic_power = 0
    change_variable = {
        name = economic_power
        value = num_of_civilian_factories
    }
    change_variable = {
        name = economic_power
        value = num_of_military_factories
    }
    
    # Military power
    var:military_power = 0
    change_variable = {
        name = military_power
        value = num_of_battalions
    }
    
    # Total power
    var:total_power = 0
    change_variable = {
        name = total_power
        value = economic_power
    }
    change_variable = {
        name = total_power
        value = military_power
    }
    
    return = total_power
}