namespace = crisis_target_selection

country_event = {
    id = crisis_target_selection.1
    title = "SELECT_CRISIS_TARGET_TITLE"
    desc = "SELECT_CRISIS_TARGET_DESC"
    picture = GFX_event_tension_rising
    
    is_triggered_only = yes
    
    # Show all valid targets
    option = {
        name = "TARGET_COUNTRY_OPTION"
        trigger = {
            any_country = {
                limit = {
                    NOT = { ROOT = this }
                    NOT = { is_subject_of = ROOT }
                    NOT = { is_in_faction_with = ROOT }
                    any_owned_state = {
                        ROOT = { has_core = PREV }
                    }
                }
            }
        }
        
        # Set the target country
        set_global_variable = { name = crisis_victim value = FROM }
        
        # Move to state selection
        country_event = { id = crisis_target_selection.2 days = 1 }
    }
}

country_event = {
    id = crisis_target_selection.2
    title = "SELECT_DISPUTED_STATE_TITLE"
    desc = "SELECT_DISPUTED_STATE_DESC"
    
    is_triggered_only = yes
    
    # Show all disputed states
    option = {
        name = "STATE_OPTION"
        trigger = {
            any_owned_state = {
                limit = {
                    ROOT = { has_core = PREV }
                }
            }
        }
        
        # Set the disputed state
        set_global_variable = { name = crisis_disputed_state value = FROM }
        
        # Start the crisis
        scripted_effect = initialize_crisis
        
        # Notify all great powers
        every_country = {
            limit = { is_great_power = yes }
            add_country_flag = crisis_aware
            country_event = { id = crisis_notification.1 days = 1 }
        }
        
        # Open crisis window
        open_crisis_window = yes
    }
}