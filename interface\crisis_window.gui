windowType = {
    name = "crisisWindow"
    
    position = { x = 200 y = 100 }
    size = { width = 800 height = 600 }
    
    background = {
        name = "background"
        spriteType = "gfx\\interface\\crisis_background.dds"
    }
    
    # Crisis intensity bar
    progressBarType = {
        name = "crisis_intensity_bar"
        position = { x = 50 y = 50 }
        size = { width = 700 height = 20 }
        
        currentValue = "[GetCrisisIntensity]"
        maxValue = "100"
        
        orientation = "horizontal"
        
        # Color coding based on intensity
        if = {
            limit = { "[GetCrisisIntensity] < 33" }
            color = { 0 255 0 }
        }
        if = {
            limit = { "[GetCrisisIntensity] >= 33 AND [GetCrisisIntensity] < 66" }
            color = { 255 255 0 }
        }
        if = {
            limit = { "[GetCrisisIntensity] >= 66" }
            color = { 255 0 0 }
        }
    }
    
    # Attacker side panel
    containerWindowType = {
        name = "attacker_panel"
        position = { x = 50 y = 100 }
        size = { width = 300 height = 400 }
        
        iconType = {
            name = "attacker_flag"
            position = { x = 10 y = 10 }
            spriteType = "GFX_country_flag"
            texture = "[GetCrisisAttackerFlag]"
        }
        
        textBoxType = {
            name = "attacker_power"
            position = { x = 10 y = 60 }
            size = { width = 280 height = 30 }
            text = "[GetCrisisAttackerPower]"
        }
        
        # Action buttons
        buttonType = {
            name = "attacker_demonstration"
            position = { x = 10 y = 350 }
            size = { width = 130 height = 30 }
            quadTextureSprite = "button_bg"
            text = "DEMONSTRATION"
            
            onClick = {
                effect = {
                    if = {
                        limit = {
                            check_global_variable = { name = crisis_active value = 1 }
                            ROOT = { has_influence > 25 }
                        }
                        ROOT = {
                            scripted_effect = demonstration_strength
                        }
                    }
                }
            }
        }
    }
    
    # Defender side panel (similar structure)
    containerWindowType = {
        name = "defender_panel"
        position = { x = 450 y = 100 }
        size = { width = 300 height = 400 }
        
        iconType = {
            name = "defender_flag"
            position = { x = 10 y = 10 }
            spriteType = "GFX_country_flag"
            texture = "[GetCrisisDefenderFlag]"
        }
        
        textBoxType = {
            name = "defender_power"
            position = { x = 10 y = 60 }
            size = { width = 280 height = 30 }
            text = "[GetCrisisDefenderPower]"
        }
    }
    
    # Crisis timer
    textBoxType = {
        name = "crisis_timer"
        position = { x = 350 y = 520 }
        size = { width = 100 height = 30 }
        text = "Day: [GetCrisisDay]"
    }
}