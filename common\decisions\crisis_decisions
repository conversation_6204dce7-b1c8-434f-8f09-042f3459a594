diplomatic_crisis_decisions = {
    # Main crisis initiation decision
    initiate_diplomatic_crisis = {
        icon = gfx_goal_icons_generic_generic_tension
        
        is_shown = {
            NOT = { has_global_variable = { name = crisis_active value = 1 } }
            is_great_power = yes
            global_tension < 0.1
            NOT = { is_at_war = yes }
            has_political_power > 100
        }
        
        available = {
            NOT = { has_global_variable = { name = crisis_active value = 1 } }
            is_great_power = yes
            global_tension < 0.1
            NOT = { is_at_war = yes }
            has_political_power > 100
        }
        
        cost = 100
        
        complete_tooltip = initiate_diplomatic_crisis_tooltip
        
        when_completed = {
            # Trigger the crisis initiation event
            country_event = { id = crisis_initiator.1 days = 1 }
        }
        
        ai_will_do = {
            factor = 0
            # AI doesn't use this decision for now
        }
    }
    
    # Decision to join existing crisis (for other great powers)
    consider_crisis_participation = {
        icon = gfx_goal_icons_generic_generic_tension
        
        is_shown = {
            has_global_variable = { name = crisis_active value = 1 }
            is_great_power = yes
            NOT = { has_country_flag = crisis_participant }
            has_country_flag = crisis_aware
        }
        
        available = {
            has_global_variable = { name = crisis_active value = 1 }
            is_great_power = yes
            NOT = { has_country_flag = crisis_participant }
            has_country_flag = crisis_aware
        }
        
        cost = 50
        
        complete_tooltip = consider_crisis_participation_tooltip
        
        when_completed = {
            # Open crisis participation interface
            open_crisis_window = yes
            add_country_flag = crisis_considering
        }
        
        ai_will_do = {
            factor = 1
            modifier = {
                factor = 2
                has_opinion > { who = crisis_initiator value > 0 }
            }
            modifier = {
                factor = 0.5
                has_opinion < { who = crisis_victim value > 0 }
            }
        }
    }
}